# Database Configuration
DATABASE_URL="mysql://root:password@localhost:3306/santrimental6"

# App Settings
NODE_ENV="development"
PORT=5000
FRONTEND_URL="http://localhost:5000"

# JWT Secret
JWT_SECRET="f5fe56f12ff535e570f04a7fa1f799441459f54c283f9d87893192cd99d007a8"

# Email Configuration (Gmail SMTP)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Google OAuth Configuration
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Offline Mode (optional)
ENABLE_OFFLINE=true

# Example for production:
# DATABASE_URL="mysql://username:password@hostname:3306/database_name"
# FRONTEND_URL="https://yourdomain.com"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-secure-app-password"
