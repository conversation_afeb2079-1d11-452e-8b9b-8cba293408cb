import mysql from 'mysql2/promise';
import { drizzle } from 'drizzle-orm/mysql2';
import * as schema from "@shared/schema";
import { DatabaseConfig } from '../config/database';

export class AivenMySQLDatabase {
  private pool: mysql.Pool;
  public db: ReturnType<typeof drizzle>;

  constructor(config: DatabaseConfig) {
    console.log('🔗 Initializing AIVEN MySQL connection...');
    
    this.pool = mysql.createPool({
      uri: config.url,
      ssl: config.ssl as mysql.SslOptions,
      waitForConnections: true,
      connectionLimit: config.poolConfig?.connectionLimit || 20,
      queueLimit: config.poolConfig?.queueLimit || 0,
      acquireTimeout: config.poolConfig?.acquireTimeout || 60000,
      timeout: config.poolConfig?.timeout || 60000,
      reconnect: true
    });

    this.db = drizzle(this.pool, { schema, mode: 'default' });
  }

  async testConnection(): Promise<boolean> {
    try {
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();
      console.log('✅ AIVEN MySQL connection successful');
      return true;
    } catch (error) {
      console.error('❌ AIVEN MySQL connection failed:', error);
      return false;
    }
  }

  async close(): Promise<void> {
    await this.pool.end();
    console.log('🔌 AIVEN MySQL connection closed');
  }

  getPool(): mysql.Pool {
    return this.pool;
  }
}