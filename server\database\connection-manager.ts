import { DatabaseFactory } from './factory';
import { getDatabaseConfig, DatabaseProvider } from '../config/database';

export class ConnectionManager {
  private static instance: ConnectionManager;
  private currentProvider: DatabaseProvider | null = null;
  private currentConnection: any = null;

  static getInstance(): ConnectionManager {
    if (!ConnectionManager.instance) {
      ConnectionManager.instance = new ConnectionManager();
    }
    return ConnectionManager.instance;
  }

  async getConnection() {
    const config = getDatabaseConfig();
    
    // Return existing connection if provider hasn't changed
    if (this.currentProvider === config.provider && this.currentConnection) {
      return this.currentConnection;
    }

    // Create new connection
    console.log(`🔄 Switching to ${config.provider} database...`);
    this.currentConnection = await DatabaseFactory.create(config);
    this.currentProvider = config.provider;

    return this.currentConnection;
  }

  getCurrentProvider(): DatabaseProvider | null {
    return this.currentProvider;
  }

  async switchProvider(provider: DatabaseProvider): Promise<void> {
    process.env.DATABASE_PROVIDER = provider;
    this.currentProvider = null;
    this.currentConnection = null;
    await this.getConnection();
  }
}