import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Navbar } from '@/components/sections/Navbar';
import { Footer } from '@/components/sections/Footer';
import { Play, Trophy, Clock, Users, Star, Gamepad2, Brain, Heart, Zap, Target } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface Game {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  category: string;
  difficulty: 'Mudah' | 'Sedang' | 'Sulit';
  duration: string;
  players: number;
  rating: number;
  tags: string[];
  isLocked: boolean;
  progress?: number;
}

const Games = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);

  // Sample games data - in production this would come from API
  const games: Game[] = [
    {
      id: '1',
      title: 'Mood Tracker Challenge',
      description: 'Game interaktif untuk melacak dan memahami perubahan mood harian. Bantu santri mengenali pola emosi mereka dengan cara yang menyenangkan.',
      thumbnail: '/api/placeholder/300/200',
      category: 'Self-Awareness',
      difficulty: 'Mudah',
      duration: '10-15 menit',
      players: 1,
      rating: 4.8,
      tags: ['mood', 'tracking', 'emosi', 'self-awareness'],
      isLocked: false,
      progress: 65
    },
    {
      id: '2',
      title: 'Stress Buster Puzzle',
      description: 'Puzzle yang dirancang khusus untuk mengurangi stres. Setiap level memberikan tips manajemen stres yang praktis untuk kehidupan santri.',
      thumbnail: '/api/placeholder/300/200',
      category: 'Stress Relief',
      difficulty: 'Sedang',
      duration: '15-20 menit',
      players: 1,
      rating: 4.6,
      tags: ['puzzle', 'stres', 'relaksasi', 'problem-solving'],
      isLocked: false,
      progress: 30
    },
    {
      id: '3',
      title: 'Empathy Builder',
      description: 'Game simulasi untuk mengembangkan empati dan keterampilan sosial. Santri belajar memahami perasaan orang lain melalui skenario interaktif.',
      thumbnail: '/api/placeholder/300/200',
      category: 'Social Skills',
      difficulty: 'Sedang',
      duration: '20-25 menit',
      players: 2,
      rating: 4.9,
      tags: ['empati', 'sosial', 'komunikasi', 'simulasi'],
      isLocked: false,
      progress: 0
    },
    {
      id: '4',
      title: 'Mindfulness Garden',
      description: 'Game berkebun virtual yang mengajarkan teknik mindfulness. Santri merawat taman sambil belajar teknik pernapasan dan meditasi Islami.',
      thumbnail: '/api/placeholder/300/200',
      category: 'Mindfulness',
      difficulty: 'Mudah',
      duration: '10-30 menit',
      players: 1,
      rating: 4.7,
      tags: ['mindfulness', 'berkebun', 'meditasi', 'relaksasi'],
      isLocked: true,
      progress: 0
    },
    {
      id: '5',
      title: 'Confidence Quest',
      description: 'Petualangan RPG untuk membangun kepercayaan diri. Santri menyelesaikan misi yang dirancang untuk meningkatkan self-esteem dan keberanian.',
      thumbnail: '/api/placeholder/300/200',
      category: 'Confidence',
      difficulty: 'Sulit',
      duration: '30-45 menit',
      players: 1,
      rating: 4.5,
      tags: ['kepercayaan diri', 'rpg', 'petualangan', 'motivasi'],
      isLocked: true,
      progress: 0
    },
    {
      id: '6',
      title: 'Team Harmony',
      description: 'Game multiplayer untuk membangun kerjasama dan komunikasi dalam kelompok. Cocok untuk aktivitas bersama di pesantren.',
      thumbnail: '/api/placeholder/300/200',
      category: 'Teamwork',
      difficulty: 'Sedang',
      duration: '25-35 menit',
      players: 4,
      rating: 4.8,
      tags: ['kerjasama', 'tim', 'komunikasi', 'multiplayer'],
      isLocked: true,
      progress: 0
    }
  ];

  const categories = ['all', 'Self-Awareness', 'Stress Relief', 'Social Skills', 'Mindfulness', 'Confidence', 'Teamwork'];

  const filteredGames = games.filter(game => {
    const matchesCategory = selectedCategory === 'all' || game.category === selectedCategory;
    return matchesCategory;
  });

  const handlePlayGame = (game: Game) => {
    if (game.isLocked) {
      toast({
        variant: 'destructive',
        title: 'Game Terkunci',
        description: 'Selesaikan assessment terlebih dahulu untuk membuka game ini'
      });
      return;
    }

    toast({
      title: 'Memulai Game',
      description: `Memuat ${game.title}...`
    });
    
    // In production, this would launch the actual game
    setSelectedGame(game);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Mudah': return 'bg-green-100 text-green-800';
      case 'Sedang': return 'bg-yellow-100 text-yellow-800';
      case 'Sulit': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={12}
        className={i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  if (selectedGame) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <Button 
            variant="ghost" 
            onClick={() => setSelectedGame(null)}
            className="mb-4 gap-2"
          >
            ← Kembali ke Daftar Game
          </Button>
          
          <div className="max-w-4xl mx-auto">
            <Card className="p-8 text-center">
              <div className="text-6xl mb-4">🎮</div>
              <h1 className="text-3xl font-bold mb-4">{selectedGame.title}</h1>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                {selectedGame.description}
              </p>
              
              <div className="flex justify-center gap-4 mb-6">
                <Badge className={getDifficultyColor(selectedGame.difficulty)}>
                  {selectedGame.difficulty}
                </Badge>
                <Badge variant="outline">
                  <Clock size={12} className="mr-1" />
                  {selectedGame.duration}
                </Badge>
                <Badge variant="outline">
                  <Users size={12} className="mr-1" />
                  {selectedGame.players} pemain
                </Badge>
              </div>
              
              <div className="bg-gradient-to-r from-primary/10 to-emerald-600/10 p-8 rounded-lg">
                <h3 className="text-xl font-semibold mb-4">Game akan segera dimuat...</h3>
                <p className="text-muted-foreground mb-4">
                  Dalam versi production, di sini akan ada game interaktif yang sesungguhnya.
                </p>
                <Button size="lg" className="gap-2">
                  <Play size={20} />
                  Mulai Bermain
                </Button>
              </div>
            </Card>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-emerald-600 bg-clip-text text-transparent mb-2">
            🎮 Game Edukasi SantriMental
          </h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Koleksi game edukasi yang dirancang untuk meningkatkan kesehatan mental santri 
            dengan cara yang menyenangkan dan interaktif.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {categories.map(category => (
            <Button
              key={category}
              variant={selectedCategory === category ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className="transition-all"
            >
              {category === 'all' ? 'Semua Game' : category}
            </Button>
          ))}
        </div>

        {/* Games Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {filteredGames.map(game => (
            <Card key={game.id} className="hover:shadow-lg transition-all duration-200 group relative overflow-hidden">
              {game.isLocked && (
                <div className="absolute top-2 right-2 z-10">
                  <Badge variant="secondary" className="bg-gray-500 text-white">
                    🔒 Terkunci
                  </Badge>
                </div>
              )}
              
              <div className="relative">
                <img src={game.thumbnail} alt={game.title} className="w-full aspect-video object-cover" />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center">
                  <Play className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" size={48} />
                </div>
              </div>
              
              <CardContent className="p-4">
                <h3 className="font-semibold line-clamp-2 mb-2 group-hover:text-primary transition-colors">
                  {game.title}
                </h3>
                <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                  {game.description}
                </p>
                
                {game.progress !== undefined && game.progress > 0 && (
                  <div className="mb-3">
                    <div className="flex justify-between text-xs mb-1">
                      <span>Progress</span>
                      <span>{game.progress}%</span>
                    </div>
                    <Progress value={game.progress} className="h-2" />
                  </div>
                )}
                
                <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                  <div className="flex items-center gap-1">
                    {renderStars(game.rating)}
                    <span className="ml-1">({game.rating})</span>
                  </div>
                  <Badge className={getDifficultyColor(game.difficulty)} variant="outline">
                    {game.difficulty}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                  <span className="flex items-center gap-1">
                    <Clock size={12} />
                    {game.duration}
                  </span>
                  <span className="flex items-center gap-1">
                    <Users size={12} />
                    {game.players} pemain
                  </span>
                </div>
                
                <div className="flex flex-wrap gap-1 mb-4">
                  {game.tags.slice(0, 2).map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>
                
                <Button 
                  onClick={() => handlePlayGame(game)}
                  className="w-full gap-2"
                  variant={game.isLocked ? 'outline' : 'default'}
                  disabled={game.isLocked}
                >
                  {game.isLocked ? (
                    <>🔒 Terkunci</>
                  ) : (
                    <>
                      <Play size={16} />
                      {game.progress && game.progress > 0 ? 'Lanjutkan' : 'Mulai Bermain'}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Game Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-primary flex items-center justify-center gap-2">
              <Gamepad2 size={24} />
              {games.length}
            </div>
            <div className="text-sm text-muted-foreground">Total Game</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-emerald-600 flex items-center justify-center gap-2">
              <Trophy size={24} />
              {games.filter(g => !g.isLocked).length}
            </div>
            <div className="text-sm text-muted-foreground">Game Tersedia</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-blue-600 flex items-center justify-center gap-2">
              <Target size={24} />
              {Math.round(games.reduce((sum, game) => sum + (game.progress || 0), 0) / games.length)}%
            </div>
            <div className="text-sm text-muted-foreground">Progress Rata-rata</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-purple-600 flex items-center justify-center gap-2">
              <Star size={24} />
              {(games.reduce((sum, game) => sum + game.rating, 0) / games.length).toFixed(1)}
            </div>
            <div className="text-sm text-muted-foreground">Rating Rata-rata</div>
          </Card>
        </div>

        {/* How to Unlock Games */}
        <Card className="bg-gradient-to-r from-primary/5 to-emerald-600/5 border-primary/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="text-primary" size={24} />
              Cara Membuka Game Baru
            </CardTitle>
            <CardDescription>
              Selesaikan assessment dan aktivitas untuk membuka game yang lebih menantang
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="text-center p-4">
                <Brain className="mx-auto mb-2 text-primary" size={32} />
                <h4 className="font-semibold mb-1">Selesaikan Assessment</h4>
                <p className="text-sm text-muted-foreground">
                  Ikuti assessment DASS-42 dan lainnya
                </p>
              </div>
              <div className="text-center p-4">
                <Heart className="mx-auto mb-2 text-emerald-600" size={32} />
                <h4 className="font-semibold mb-1">Konsisten Bermain</h4>
                <p className="text-sm text-muted-foreground">
                  Mainkan game secara rutin untuk progress
                </p>
              </div>
              <div className="text-center p-4">
                <Trophy className="mx-auto mb-2 text-yellow-600" size={32} />
                <h4 className="font-semibold mb-1">Raih Achievement</h4>
                <p className="text-sm text-muted-foreground">
                  Kumpulkan poin dan badge untuk unlock
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Footer />
    </div>
  );
};

export default Games;
