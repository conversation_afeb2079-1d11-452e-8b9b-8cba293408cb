import { DatabaseConfig, DatabaseProvider } from '../config/database';
import { AivenMySQLDatabase } from './aiven-mysql';
import { pool as mysqlPool, db as mysqlDb } from '../db';
import { sqliteDb } from '../sqlite-db';

export class DatabaseFactory {
  private static instances: Map<DatabaseProvider, any> = new Map();

  static async create(config: DatabaseConfig) {
    const { provider } = config;

    // Return existing instance if available
    if (this.instances.has(provider)) {
      return this.instances.get(provider);
    }

    let instance;

    switch (provider) {
      case 'aiven-mysql':
        instance = new AivenMySQLDatabase(config);
        await instance.testConnection();
        this.instances.set(provider, instance);
        console.log(`✅ ${provider} database initialized`);
        return instance;

      case 'mysql':
        // Use existing MySQL connection
        instance = { db: mysqlDb, pool: mysqlPool };
        this.instances.set(provider, instance);
        console.log(`✅ ${provider} database initialized`);
        return instance;

      case 'sqlite':
        instance = { db: sqliteDb };
        this.instances.set(provider, instance);
        console.log(`✅ ${provider} database initialized`);
        return instance;

      default:
        throw new Error(`Unsupported database provider: ${provider}`);
    }
  }

  static async closeAll(): Promise<void> {
    for (const [provider, instance] of this.instances.entries()) {
      if (instance.close) {
        await instance.close();
      }
      console.log(`🔌 ${provider} connection closed`);
    }
    this.instances.clear();
  }
}