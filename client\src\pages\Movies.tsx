import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Navbar } from '@/components/sections/Navbar';
import { Footer } from '@/components/sections/Footer';
import { Search, Play, Clock, Eye, ThumbsUp, Share2, Download, Filter, Grid, List } from 'lucide-react';

interface Movie {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  videoUrl: string;
  duration: string;
  views: number;
  likes: number;
  category: string;
  uploadDate: string;
  tags: string[];
}

const Movies = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedMovie, setSelectedMovie] = useState<Movie | null>(null);

  // Sample movie data - in production this would come from API
  const movies: Movie[] = [
    {
      id: '1',
      title: 'Mengelola Stres dalam Kehidupan Santri',
      description: 'Video edukasi tentang cara mengelola stres dan tekanan dalam kehidupan pesantren. Membahas teknik relaksasi Islami dan manajemen waktu yang efektif.',
      thumbnail: '/api/placeholder/320/180',
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
      duration: '15:30',
      views: 1250,
      likes: 89,
      category: 'Kesehatan Mental',
      uploadDate: '2024-01-15',
      tags: ['stres', 'santri', 'relaksasi', 'islami']
    },
    {
      id: '2',
      title: 'Membangun Kepercayaan Diri Santri',
      description: 'Panduan praktis untuk membangun kepercayaan diri yang sehat berdasarkan nilai-nilai Islam. Cocok untuk santri yang mengalami rasa minder atau kurang percaya diri.',
      thumbnail: '/api/placeholder/320/180',
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
      duration: '12:45',
      views: 980,
      likes: 67,
      category: 'Pengembangan Diri',
      uploadDate: '2024-01-10',
      tags: ['kepercayaan diri', 'santri', 'motivasi']
    },
    {
      id: '3',
      title: 'Mengatasi Homesick di Pesantren',
      description: 'Tips dan strategi untuk mengatasi rasa rindu rumah yang berlebihan. Membahas cara adaptasi dan membangun ikatan sosial yang sehat di lingkungan pesantren.',
      thumbnail: '/api/placeholder/320/180',
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
      duration: '18:20',
      views: 1450,
      likes: 102,
      category: 'Adaptasi',
      uploadDate: '2024-01-05',
      tags: ['homesick', 'adaptasi', 'pesantren']
    },
    {
      id: '4',
      title: 'Teknik Mindfulness dalam Islam',
      description: 'Pengenalan teknik mindfulness yang sesuai dengan ajaran Islam. Membahas dzikir, muraqabah, dan teknik meditasi Islami untuk kesehatan mental.',
      thumbnail: '/api/placeholder/320/180',
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
      duration: '22:15',
      views: 2100,
      likes: 156,
      category: 'Spiritual',
      uploadDate: '2024-01-01',
      tags: ['mindfulness', 'dzikir', 'spiritual', 'meditasi']
    }
  ];

  const categories = ['all', 'Kesehatan Mental', 'Pengembangan Diri', 'Adaptasi', 'Spiritual'];

  const filteredMovies = movies.filter(movie => {
    const matchesSearch = movie.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         movie.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         movie.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || movie.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const formatViews = (views: number) => {
    if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`;
    }
    return views.toString();
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  if (selectedMovie) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <Button 
            variant="ghost" 
            onClick={() => setSelectedMovie(null)}
            className="mb-4 gap-2"
          >
            ← Kembali ke Daftar Video
          </Button>
          
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Video Player */}
            <div className="lg:col-span-2">
              <div className="aspect-video bg-black rounded-lg overflow-hidden mb-4">
                <iframe
                  src={selectedMovie.videoUrl}
                  title={selectedMovie.title}
                  className="w-full h-full"
                  allowFullScreen
                />
              </div>
              
              <div className="space-y-4">
                <h1 className="text-2xl font-bold">{selectedMovie.title}</h1>
                
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Eye size={16} />
                    {formatViews(selectedMovie.views)} views
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock size={16} />
                    {selectedMovie.duration}
                  </span>
                  <span>{formatDate(selectedMovie.uploadDate)}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" className="gap-2">
                    <ThumbsUp size={16} />
                    {selectedMovie.likes}
                  </Button>
                  <Button variant="outline" size="sm" className="gap-2">
                    <Share2 size={16} />
                    Bagikan
                  </Button>
                  <Button variant="outline" size="sm" className="gap-2">
                    <Download size={16} />
                    Download
                  </Button>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary">{selectedMovie.category}</Badge>
                  <p className="text-muted-foreground leading-relaxed">
                    {selectedMovie.description}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {selectedMovie.tags.map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        #{tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            
            {/* Sidebar - Related Videos */}
            <div className="space-y-4">
              <h3 className="font-semibold">Video Terkait</h3>
              {movies.filter(m => m.id !== selectedMovie.id).slice(0, 5).map(movie => (
                <Card key={movie.id} className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setSelectedMovie(movie)}>
                  <CardContent className="p-3">
                    <div className="flex gap-3">
                      <div className="relative flex-shrink-0">
                        <img src={movie.thumbnail} alt={movie.title} className="w-24 h-14 object-cover rounded" />
                        <div className="absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1 rounded">
                          {movie.duration}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm line-clamp-2 mb-1">{movie.title}</h4>
                        <p className="text-xs text-muted-foreground">
                          {formatViews(movie.views)} views • {formatDate(movie.uploadDate)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-emerald-600 bg-clip-text text-transparent mb-2">
            🎬 Film Edukasi SantriMental
          </h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Koleksi video edukasi kesehatan mental yang dirancang khusus untuk santri pesantren. 
            Belajar dengan cara yang menyenangkan dan sesuai nilai-nilai Islam.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={20} />
            <Input
              placeholder="Cari video edukasi..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex gap-2">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'Semua Kategori' : category}
                </option>
              ))}
            </select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            >
              {viewMode === 'grid' ? <List size={16} /> : <Grid size={16} />}
            </Button>
          </div>
        </div>

        {/* Video Grid/List */}
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' : 'space-y-4'}>
          {filteredMovies.map(movie => (
            <Card key={movie.id} className="cursor-pointer hover:shadow-lg transition-all duration-200 group" onClick={() => setSelectedMovie(movie)}>
              <div className="relative">
                <img src={movie.thumbnail} alt={movie.title} className="w-full aspect-video object-cover rounded-t-lg" />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 rounded-t-lg flex items-center justify-center">
                  <Play className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" size={48} />
                </div>
                <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
                  {movie.duration}
                </div>
              </div>
              
              <CardContent className="p-4">
                <h3 className="font-semibold line-clamp-2 mb-2 group-hover:text-primary transition-colors">
                  {movie.title}
                </h3>
                <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                  {movie.description}
                </p>
                
                <div className="flex items-center justify-between text-xs text-muted-foreground mb-2">
                  <span className="flex items-center gap-1">
                    <Eye size={12} />
                    {formatViews(movie.views)}
                  </span>
                  <span className="flex items-center gap-1">
                    <ThumbsUp size={12} />
                    {movie.likes}
                  </span>
                  <span>{formatDate(movie.uploadDate)}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <Badge variant="secondary" className="text-xs">
                    {movie.category}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredMovies.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🎬</div>
            <h3 className="text-xl font-semibold mb-2">Tidak ada video ditemukan</h3>
            <p className="text-muted-foreground">
              Coba ubah kata kunci pencarian atau pilih kategori yang berbeda
            </p>
          </div>
        )}
      </div>
      
      <Footer />
    </div>
  );
};

export default Movies;
