import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertUserSchema, insertAssessmentSessionSchema, insertAssessmentResultSchema, insertProfileSchema } from "@shared/schema";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";
import passport from "passport";
import { Strategy as GoogleStrategy } from "passport-google-oauth20";
import { sendWelcomeEmail } from "./email-service";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

// Google OAuth configuration
passport.use(new GoogleStrategy({
  clientID: process.env.GOOGLE_CLIENT_ID || "your-google-client-id",
  clientSecret: process.env.GOOGLE_CLIENT_SECRET || "your-google-client-secret",
  callbackURL: "/api/auth/google/callback"
}, async (_accessToken, _refreshToken, profile, done) => {
  try {
    // Check if user exists
    let user = await storage.getUserByEmail(profile.emails?.[0]?.value || '');

    if (!user) {
      // Create new user
      user = await storage.createUser({
        email: profile.emails?.[0]?.value || '',
        password: '', // No password for Google users
      });

      // Send welcome email
      if (user.email) {
        await sendWelcomeEmail(user.email);
      }
    }

    return done(null, user);
  } catch (error) {
    return done(error, false);
  }
}));

// Middleware to verify JWT token
function authenticateToken(req: any, res: any, next: any) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err: any, user: any) => {
    if (err) return res.status(403).json({ message: 'Invalid token' });
    req.user = user;
    next();
  });
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Initialize passport
  app.use(passport.initialize());

  // Google OAuth routes
  app.get("/api/auth/google",
    passport.authenticate("google", { scope: ["profile", "email"] })
  );

  app.get("/api/auth/google/callback",
    passport.authenticate("google", { session: false }),
    async (req: any, res) => {
      try {
        const user = req.user;
        if (!user) {
          return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5000'}/login?error=auth_failed`);
        }

        // Generate JWT token
        const token = jwt.sign({ userId: user.id, email: user.email }, JWT_SECRET, { expiresIn: '24h' });

        // Redirect to frontend with token
        res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5000'}/login?token=${token}`);
      } catch (error) {
        res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5000'}/login?error=server_error`);
      }
    }
  );

  // Auth routes
  app.post("/api/auth/signup", async (req, res) => {
    try {
      const validatedData = insertUserSchema.parse(req.body);
      
      // Check if user already exists
      const existingUser = await storage.getUserByEmail(validatedData.email);
      if (existingUser) {
        return res.status(400).json({ message: "User already exists" });
      }
      
      // Hash password
      const hashedPassword = await bcrypt.hash(validatedData.password, 10);
      
      // Create user
      const user = await storage.createUser({
        ...validatedData,
        password: hashedPassword
      });

      // Send welcome email
      try {
        await sendWelcomeEmail(user.email);
        console.log(`✅ Welcome email sent to ${user.email}`);
      } catch (emailError) {
        console.error(`❌ Failed to send welcome email to ${user.email}:`, emailError);
        // Don't fail the registration if email fails
      }

      // Generate JWT token
      const token = jwt.sign({ userId: user.id, email: user.email }, JWT_SECRET, { expiresIn: '24h' });

      res.json({
        user: { id: user.id, email: user.email },
        access_token: token
      });
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.post("/api/auth/signin", async (req, res) => {
    try {
      const { email, password } = req.body;
      
      // Find user
      const user = await storage.getUserByEmail(email);
      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }
      
      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        return res.status(401).json({ message: "Invalid credentials" });
      }
      
      // Generate JWT token
      const token = jwt.sign({ userId: user.id, email: user.email }, JWT_SECRET, { expiresIn: '24h' });
      
      res.json({ 
        user: { id: user.id, email: user.email }, 
        access_token: token 
      });
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.get("/api/auth/user", authenticateToken, async (req: any, res) => {
    try {
      const user = await storage.getUser(req.user.userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      res.json({ id: user.id, email: user.email });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Assessment routes
  app.get("/api/assessment-configs", async (req, res) => {
    try {
      const configs = await storage.getAssessmentConfigs();
      res.json(configs);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.post("/api/assessment-sessions", authenticateToken, async (req: any, res) => {
    try {
      const validatedData = insertAssessmentSessionSchema.parse({
        ...req.body,
        userId: req.user.userId
      });

      const session = await storage.createAssessmentSession(validatedData);
      res.json(session);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.post("/api/assessment-results", authenticateToken, async (req: any, res) => {
    try {
      const validatedData = insertAssessmentResultSchema.parse({
        ...req.body,
        userId: req.user.userId
      });

      const result = await storage.createAssessmentResult(validatedData);
      res.json(result);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.get("/api/assessment-results", authenticateToken, async (req: any, res) => {
    try {
      const results = await storage.getAssessmentResultsByUser(req.user.userId);
      res.json(results);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Sync status endpoint
  app.get("/api/sync/status", authenticateToken, async (req: any, res) => {
    try {
      if ('getSyncStatus' in storage) {
        const status = await (storage as any).getSyncStatus();
        res.json(status);
      } else {
        res.json({ pending: 0, lastSync: new Date(), isOnline: true });
      }
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Manual sync trigger
  app.post("/api/sync/trigger", authenticateToken, async (req: any, res) => {
    try {
      if ('syncToServer' in storage) {
        await (storage as any).syncToServer();
        res.json({ message: "Sync triggered successfully" });
      } else {
        res.json({ message: "Sync not available in current storage mode" });
      }
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Profile routes
  app.get("/api/profile", authenticateToken, async (req: any, res) => {
    try {
      const profile = await storage.getProfile(req.user.userId);
      res.json(profile);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.post("/api/profile", authenticateToken, async (req: any, res) => {
    try {
      const validatedData = insertProfileSchema.parse({
        ...req.body,
        userId: req.user.userId
      });
      
      const profile = await storage.createProfile(validatedData);
      res.json(profile);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.put("/api/profile", authenticateToken, async (req: any, res) => {
    try {
      const validatedData = insertProfileSchema.partial().parse(req.body);
      
      const profile = await storage.updateProfile(req.user.userId, validatedData);
      if (!profile) {
        return res.status(404).json({ message: "Profile not found" });
      }
      
      res.json(profile);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
