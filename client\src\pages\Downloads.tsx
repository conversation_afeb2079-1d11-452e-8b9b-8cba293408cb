import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Navbar } from '@/components/sections/Navbar';
import { Footer } from '@/components/sections/Footer';
import { Search, Download, FileText, Calendar, User, Star, Filter, Grid, List, Eye } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface DownloadItem {
  id: string;
  title: string;
  description: string;
  fileUrl: string;
  fileSize: string;
  fileType: 'PDF' | 'DOC' | 'PPT';
  category: string;
  author: string;
  uploadDate: string;
  downloads: number;
  rating: number;
  tags: string[];
  thumbnail?: string;
}

const Downloads = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Sample download data - in production this would come from API
  const downloadItems: DownloadItem[] = [
    {
      id: '1',
      title: 'Panduan Kesehatan Mental Santri',
      description: 'Buku panduan lengkap tentang kesehatan mental untuk santri pesantren. Membahas berbagai aspek psikologis dalam kehidupan santri dan cara mengatasinya.',
      fileUrl: '/downloads/panduan-kesehatan-mental-santri.pdf',
      fileSize: '2.5 MB',
      fileType: 'PDF',
      category: 'Panduan',
      author: 'Dr. Ahmad Santoso, M.Psi',
      uploadDate: '2024-01-15',
      downloads: 1250,
      rating: 4.8,
      tags: ['panduan', 'kesehatan mental', 'santri', 'psikologi']
    },
    {
      id: '2',
      title: 'Worksheet Assessment DASS-42',
      description: 'Lembar kerja untuk assessment Depression, Anxiety, and Stress Scale. Dapat digunakan untuk self-assessment atau dengan bimbingan konselor.',
      fileUrl: '/downloads/worksheet-dass42.pdf',
      fileSize: '1.2 MB',
      fileType: 'PDF',
      category: 'Assessment',
      author: 'Tim SantriMental',
      uploadDate: '2024-01-12',
      downloads: 890,
      rating: 4.6,
      tags: ['assessment', 'dass42', 'worksheet', 'depresi', 'kecemasan']
    },
    {
      id: '3',
      title: 'Teknik Relaksasi Islami',
      description: 'Kumpulan teknik relaksasi yang sesuai dengan ajaran Islam. Termasuk panduan dzikir, doa, dan meditasi Islami untuk menenangkan pikiran.',
      fileUrl: '/downloads/teknik-relaksasi-islami.pdf',
      fileSize: '3.1 MB',
      fileType: 'PDF',
      category: 'Spiritual',
      author: 'Ustadz Muhammad Ridwan, Lc',
      uploadDate: '2024-01-10',
      downloads: 1450,
      rating: 4.9,
      tags: ['relaksasi', 'islami', 'dzikir', 'meditasi', 'spiritual']
    },
    {
      id: '4',
      title: 'Jurnal Harian Santri',
      description: 'Template jurnal harian untuk membantu santri memantau kondisi mental dan spiritual mereka. Dilengkapi dengan panduan pengisian.',
      fileUrl: '/downloads/jurnal-harian-santri.pdf',
      fileSize: '800 KB',
      fileType: 'PDF',
      category: 'Tools',
      author: 'Tim SantriMental',
      uploadDate: '2024-01-08',
      downloads: 670,
      rating: 4.5,
      tags: ['jurnal', 'harian', 'monitoring', 'self-care']
    },
    {
      id: '5',
      title: 'Materi Edukasi Kesehatan Mental',
      description: 'Presentasi PowerPoint tentang dasar-dasar kesehatan mental. Cocok untuk digunakan dalam sesi edukasi atau diskusi kelompok.',
      fileUrl: '/downloads/materi-edukasi-kesehatan-mental.pptx',
      fileSize: '5.2 MB',
      fileType: 'PPT',
      category: 'Edukasi',
      author: 'Dr. Siti Aminah, M.Psi',
      uploadDate: '2024-01-05',
      downloads: 420,
      rating: 4.7,
      tags: ['edukasi', 'presentasi', 'kesehatan mental', 'diskusi']
    }
  ];

  const categories = ['all', 'Panduan', 'Assessment', 'Spiritual', 'Tools', 'Edukasi'];

  const filteredItems = downloadItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleDownload = (item: DownloadItem) => {
    // In production, this would trigger actual download
    toast({
      title: 'Download Dimulai',
      description: `Mengunduh ${item.title}...`
    });
    
    // Simulate download
    setTimeout(() => {
      toast({
        title: 'Download Selesai',
        description: `${item.title} berhasil diunduh!`
      });
    }, 2000);
  };

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'PDF':
        return <FileText className="text-red-500" size={20} />;
      case 'DOC':
        return <FileText className="text-blue-500" size={20} />;
      case 'PPT':
        return <FileText className="text-orange-500" size={20} />;
      default:
        return <FileText className="text-gray-500" size={20} />;
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={12}
        className={i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-emerald-600 bg-clip-text text-transparent mb-2">
            📚 Download Materi Pendukung
          </h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Koleksi materi pendukung berupa PDF, dokumen, dan presentasi untuk mendukung 
            pembelajaran kesehatan mental santri pesantren.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={20} />
            <Input
              placeholder="Cari materi..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex gap-2">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'Semua Kategori' : category}
                </option>
              ))}
            </select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            >
              {viewMode === 'grid' ? <List size={16} /> : <Grid size={16} />}
            </Button>
          </div>
        </div>

        {/* Downloads Grid/List */}
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          {filteredItems.map(item => (
            <Card key={item.id} className="hover:shadow-lg transition-all duration-200 group">
              <CardHeader className="pb-3">
                <div className="flex items-start gap-3">
                  {getFileIcon(item.fileType)}
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg line-clamp-2 group-hover:text-primary transition-colors">
                      {item.title}
                    </CardTitle>
                    <CardDescription className="line-clamp-2">
                      {item.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <User size={12} />
                    {item.author}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar size={12} />
                    {new Date(item.uploadDate).toLocaleDateString('id-ID')}
                  </span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1">
                      {renderStars(item.rating)}
                      <span className="text-xs text-muted-foreground ml-1">
                        ({item.rating})
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Download size={12} />
                      {item.downloads}
                    </span>
                    <span>•</span>
                    <span>{item.fileSize}</span>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-1 mb-3">
                  {item.tags.slice(0, 3).map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    onClick={() => handleDownload(item)}
                    className="flex-1 gap-2"
                    size="sm"
                  >
                    <Download size={16} />
                    Download
                  </Button>
                  <Button variant="outline" size="sm">
                    <Eye size={16} />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📚</div>
            <h3 className="text-xl font-semibold mb-2">Tidak ada materi ditemukan</h3>
            <p className="text-muted-foreground">
              Coba ubah kata kunci pencarian atau pilih kategori yang berbeda
            </p>
          </div>
        )}

        {/* Stats */}
        <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-primary">{downloadItems.length}</div>
            <div className="text-sm text-muted-foreground">Total Materi</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-emerald-600">
              {downloadItems.reduce((sum, item) => sum + item.downloads, 0)}
            </div>
            <div className="text-sm text-muted-foreground">Total Download</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-blue-600">{categories.length - 1}</div>
            <div className="text-sm text-muted-foreground">Kategori</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-purple-600">
              {(downloadItems.reduce((sum, item) => sum + item.rating, 0) / downloadItems.length).toFixed(1)}
            </div>
            <div className="text-sm text-muted-foreground">Rating Rata-rata</div>
          </Card>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default Downloads;
